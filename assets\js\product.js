/**
 * Product page JavaScript for DmrThema
 */

jQuery(document).ready(function($) {
    
    // Product gallery lightbox
    if (typeof $.fn.magnificPopup !== 'undefined') {
        $('.woocommerce-product-gallery__image a').magnificPopup({
            type: 'image',
            gallery: {
                enabled: true
            }
        });
    }
    
    // Product tabs
    $('.woocommerce-tabs .tabs li a').on('click', function(e) {
        e.preventDefault();
        
        var target = $(this).attr('href');
        
        // Remove active class from all tabs and panels
        $('.woocommerce-tabs .tabs li').removeClass('active');
        $('.woocommerce-tabs .panel').hide();
        
        // Add active class to clicked tab and show corresponding panel
        $(this).parent().addClass('active');
        $(target).show();
    });
    
    // Quantity buttons
    $(document).on('click', '.quantity .plus', function() {
        var $input = $(this).siblings('.qty');
        var val = parseInt($input.val()) || 0;
        var max = parseInt($input.attr('max')) || 999;
        
        if (val < max) {
            $input.val(val + 1).trigger('change');
        }
    });
    
    $(document).on('click', '.quantity .minus', function() {
        var $input = $(this).siblings('.qty');
        var val = parseInt($input.val()) || 0;
        var min = parseInt($input.attr('min')) || 1;
        
        if (val > min) {
            $input.val(val - 1).trigger('change');
        }
    });
    
    // Product variations
    $('.variations select').on('change', function() {
        var $form = $(this).closest('form.variations_form');
        $form.trigger('woocommerce_variation_select_change');
    });
    
    // Add to cart AJAX
    $(document).on('click', '.single_add_to_cart_button:not(.disabled)', function(e) {
        var $button = $(this);
        var $form = $button.closest('form.cart');
        
        if ($form.length === 0) {
            return;
        }
        
        // Check if product is variable and variation is selected
        if ($form.hasClass('variations_form')) {
            var variationId = $form.find('input[name="variation_id"]').val();
            if (!variationId || variationId === '0') {
                return; // Let default behavior handle the error
            }
        }
        
        e.preventDefault();
        
        $button.addClass('loading');
        
        var formData = $form.serialize();
        formData += '&add-to-cart=' + $form.find('[name="add-to-cart"]').val();
        
        $.ajax({
            type: 'POST',
            url: wc_add_to_cart_params.wc_ajax_url.toString().replace('%%endpoint%%', 'add_to_cart'),
            data: formData,
            success: function(response) {
                if (response.error && response.product_url) {
                    window.location = response.product_url;
                    return;
                }
                
                // Trigger cart update
                $(document.body).trigger('added_to_cart', [response.fragments, response.cart_hash, $button]);
                
                // Show success message
                if (response.fragments) {
                    // Update cart fragments
                    $.each(response.fragments, function(key, value) {
                        $(key).replaceWith(value);
                    });
                }
            },
            error: function() {
                // Handle error
                console.log('Add to cart failed');
            },
            complete: function() {
                $button.removeClass('loading');
            }
        });
    });
    
    // Product image zoom
    if (typeof $.fn.zoom !== 'undefined') {
        $('.woocommerce-product-gallery__image').zoom();
    }
    
    // Product gallery slider
    if ($('.woocommerce-product-gallery').length > 0) {
        $('.woocommerce-product-gallery').flexslider({
            animation: "slide",
            controlNav: "thumbnails",
            animationLoop: false,
            slideshow: false,
            itemWidth: 400,
            itemMargin: 5,
            asNavFor: '.woocommerce-product-gallery'
        });
    }
    
    // Reviews toggle
    $('.woocommerce-review-link').on('click', function(e) {
        e.preventDefault();
        $('.woocommerce-tabs .tabs li.reviews_tab a').trigger('click');
        $('html, body').animate({
            scrollTop: $('.woocommerce-tabs').offset().top - 100
        }, 500);
    });

    // Move rating stars to submit button area - Basit ve etkili yontem
    function moveRatingStars() {
        var $commentForm = $('#commentform');
        var $ratingDiv = $commentForm.find('div p.comment-form-rating, > div > p');
        var $submitP = $commentForm.find('p.form-submit');

        if ($ratingDiv.length && $submitP.length) {
            // Rating div'ini gizle
            $ratingDiv.hide();

            // Eger zaten tasinmissa tekrar tasima
            if ($submitP.find('.moved-rating').length) {
                return;
            }

            // Rating iceriğini submit alanina tasi
            var ratingContent = $ratingDiv.html();
            if (ratingContent) {
                var $movedRating = $('<div class="moved-rating comment-form-rating">' + ratingContent + '</div>');
                $submitP.prepend($movedRating);

                // Yildiz tiklamalarini yeniden bagla
                $movedRating.find('.stars a, a').on('click', function(e) {
                    e.preventDefault();
                    var rating = $(this).text() || $(this).attr('data-rating') || $(this).index() + 1;

                    // Hidden input'u guncelle
                    var $hiddenInput = $commentForm.find('#rating, input[name="rating"]');
                    if ($hiddenInput.length) {
                        $hiddenInput.val(rating);
                    }

                    // Aktif yildizlari guncelle
                    $movedRating.find('a').removeClass('active');
                    $(this).addClass('active').prevAll().addClass('active');
                });
            }
        }
    }

    // CSS ile yildiz tiklama fonksiyonu
    function addStarClickHandler() {
        $(document).on('click', '#commentform p.form-submit::before', function(e) {
            var clickX = e.offsetX;
            var totalWidth = $(this).width();
            var starWidth = totalWidth / 5;
            var rating = Math.ceil(clickX / starWidth);

            // Hidden input'u guncelle
            var $hiddenInput = $('#commentform #rating, #commentform input[name="rating"]');
            if ($hiddenInput.length) {
                $hiddenInput.val(rating);
            }

            // Yildizlari guncelle
            var stars = '★'.repeat(rating) + '☆'.repeat(5 - rating);
            $(this).text('Değerlendirmeniz: ' + stars);
        });
    }

    // Sayfa yuklendiginde calistir
    $(document).ready(function() {
        // Biraz bekle ki WooCommerce formu tam yuklensin
        setTimeout(function() {
            moveRatingStars();
            addStarClickHandler();
        }, 1000);

        // Tab degistiginde de kontrol et
        $(document).on('click', '.woocommerce-tabs .tabs a', function() {
            setTimeout(function() {
                moveRatingStars();
            }, 500);
        });

        // Form yuklendiginde kontrol et
        $(document).on('DOMSubtreeModified DOMNodeInserted', function() {
            if ($('#commentform').length && !$('#commentform .moved-rating').length) {
                setTimeout(moveRatingStars, 200);
            }
        });
    });
    
    // Stock status updates
    $(document).on('found_variation', function(event, variation) {
        if (variation.is_in_stock) {
            $('.stock').removeClass('out-of-stock').addClass('in-stock');
        } else {
            $('.stock').removeClass('in-stock').addClass('out-of-stock');
        }
    });
    
    // Reset variations
    $(document).on('reset_data', function() {
        $('.stock').removeClass('out-of-stock in-stock');
    });
    
});
